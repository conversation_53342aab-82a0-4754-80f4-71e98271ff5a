package services

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"ops-api/internal/core/domain"
	"ops-api/internal/core/dto"
	"ops-api/internal/core/ports"
	"os"
	"os/exec"
	"strconv"
	"strings"
	"time"
)

type OperationService struct {
	clusterRepo           ports.ClusterRepository
	jobService            ports.JobService
	clusterService        ports.ClusterService
	deploymentService     ports.DeploymentService
	serviceService        ports.ServiceService
	ingressService        ports.IngressService
	namespaceService      ports.NamespaceService
	DnsService            ports.DnsService
	orderService          ports.OrderService
	orderNamespaceService ports.OrderNamespaceService
	domainRepo            ports.DomainRepository
}

func NewOperationService(
	clusterRepo ports.ClusterRepository,
	jobService ports.JobService,
	cluster ports.ClusterService,
	deployment ports.DeploymentService,
	service ports.ServiceService,
	ingress ports.IngressService,
	namespace ports.NamespaceService,
	dnsService ports.DnsService,
	orderService ports.OrderService,
	orderNamespaceService ports.OrderNamespaceService,
	domainRepo ports.DomainRepository,
) ports.OperationService {
	return &OperationService{
		clusterRepo:           clusterRepo,
		jobService:            jobService,
		clusterService:        cluster,
		deploymentService:     deployment,
		serviceService:        service,
		ingressService:        ingress,
		namespaceService:      namespace,
		DnsService:            dnsService,
		orderService:          orderService,
		orderNamespaceService: orderNamespaceService,
		domainRepo:            domainRepo,
	}
}

type JobRequest struct {
	ID          *uint64          `json:"id"`
	Name        string           `json:"name"`
	Description string           `json:"description"`
	StatusID    uint64           `json:"status_id"`
	EventID     *uint64          `json:"event_id,omitempty"`
	Event       domain.JobEvent  `json:"event,omitempty"`
	Action      domain.JobAction `json:"action,omitempty"`
}

// TerraformOutput represents a single Terraform output value
type TerraformOutput struct {
	Value     interface{} `json:"value"`
	Type      interface{} `json:"type"`
	Sensitive bool        `json:"sensitive"`
}

// TerraformOutputs represents all Terraform outputs
type TerraformOutputs map[string]TerraformOutput

// MockAPIResponse represents a generic API response structure
type MockAPIResponse struct {
	Status  string      `json:"status"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// MockAPICall makes a GET request to the specified URL with x-api-key header
func (s *OperationService) CallApi(url, apiKey string) (*MockAPIResponse, error) {
	// Create HTTP client with timeout
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	// Create GET request
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	// Add headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	req.Header.Set("x-api-key", apiKey)

	// Make the request
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %v", err)
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %v", err)
	}

	// Check status code
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	// Parse JSON response
	var apiResponse MockAPIResponse
	if err := json.Unmarshal(body, &apiResponse); err != nil {
		// Try to parse as a direct data object (without wrapper)
		var directData interface{}
		if jsonErr := json.Unmarshal(body, &directData); jsonErr == nil {
			apiResponse = MockAPIResponse{
				Status:  "success",
				Message: "Agent credentials retrieved successfully",
				Data:    directData,
			}
		} else {
			// If all JSON parsing fails, create a generic response with the raw body
			apiResponse = MockAPIResponse{
				Status:  "success",
				Message: "Request completed successfully",
				Data:    string(body),
			}
		}
	}

	return &apiResponse, nil
}

// parseAgentCredentials extracts agent credentials from the API response
func (s *OperationService) parseAgentCredentials(response *MockAPIResponse) (dto.AgentRequest, error) {
	fmt.Printf("Parsing agent credentials from response data: %+v (type: %T)\n", response.Data, response.Data)

	// Handle case where response.Data is a map (parsed JSON)
	if dataMap, ok := response.Data.(map[string]interface{}); ok {
		fmt.Printf("Response data is a map: %+v\n", dataMap)

		secretKey, hasSecretKey := dataMap["secretKey"]
		lineCode, hasLineCode := dataMap["lineCode"]

		fmt.Printf("secretKey found: %v, lineCode found: %v\n", hasSecretKey, hasLineCode)

		if hasSecretKey && hasLineCode {
			secretKeyStr, secretKeyOk := secretKey.(string)
			lineCodeStr, lineCodeOk := lineCode.(string)

			fmt.Printf("secretKey conversion ok: %v, lineCode conversion ok: %v\n", secretKeyOk, lineCodeOk)

			if secretKeyOk && lineCodeOk {
				fmt.Printf("Successfully parsed agent credentials: lineCode=%s, secretKey=%s\n", lineCodeStr, secretKeyStr)
				return dto.AgentRequest{
					SecretKey: secretKeyStr,
					LineCode:  lineCodeStr,
				}, nil
			}
		}
	}

	// Handle case where response.Data is a string (raw JSON)
	if dataStr, ok := response.Data.(string); ok {
		fmt.Printf("Response data is a string, attempting to parse JSON: %s\n", dataStr)

		var agentData struct {
			SecretKey string `json:"secretKey"`
			LineCode  string `json:"lineCode"`
		}

		if err := json.Unmarshal([]byte(dataStr), &agentData); err == nil {
			fmt.Printf("Parsed JSON data: %+v\n", agentData)
			if agentData.SecretKey != "" && agentData.LineCode != "" {
				fmt.Printf("Successfully parsed agent credentials from JSON string: lineCode=%s, secretKey=%s\n", agentData.LineCode, agentData.SecretKey)
				return dto.AgentRequest{
					SecretKey: agentData.SecretKey,
					LineCode:  agentData.LineCode,
				}, nil
			}
		} else {
			fmt.Printf("Failed to parse JSON string: %v\n", err)
		}
	}

	return dto.AgentRequest{}, fmt.Errorf("unable to parse agent credentials from response data: %+v", response.Data)
}

func (s *OperationService) GetOperationData(clusterId, namespaceId uint64) (dto.OperationData, error) {
	cluster, err := s.clusterRepo.FindByID(clusterId, namespaceId)
	if err != nil {
		return dto.OperationData{}, err
	}
	if cluster == nil {
		return dto.OperationData{}, errors.New("cluster not found")
	}

	// find default domain
	isDefault := true
	filter := &ports.DomainFilter{
		NamespaceID: &namespaceId,
		IsDefault:   &isDefault,
	}

	domains, err := s.domainRepo.FindAll(filter)
	if err != nil {
		return dto.OperationData{}, err
	}

	masterDomain := "{{DOMAIN}}"
	if len(domains) > 0 {
		masterDomain = domains[0].Name
	}

	namespace, err := s.namespaceService.GetByID(namespaceId)
	if err != nil {
		return dto.OperationData{}, err
	}

	agentEndpoint := os.Getenv("AGENT_ENDPOINT")
	apiURL := fmt.Sprintf("%s/agents/credentials?username=%s", agentEndpoint, namespace.Name)
	apiKey := os.Getenv("AGENT_API_KEY")

	fmt.Printf("Making AGENT_ENDPOINT call to: %s\n", apiURL)
	fmt.Printf("Using AGENT_API_KEY key: %s\n", apiKey)

	response, err := s.CallApi(apiURL, apiKey)
	if err != nil {
		fmt.Printf("API call failed: %v\n", err)
		return dto.OperationData{}, err
	}

	fmt.Printf("API call successful: %+v\n", response)

	// Parse the agent credentials from the API response
	agent, err := s.parseAgentCredentials(response)
	if err != nil {
		fmt.Printf("Failed to parse agent credentials: %v\n", err)
		return dto.OperationData{}, err
	}

	return dto.ToOperationDataDTO(*cluster, masterDomain, agent), nil
}

func (c *OperationService) CreateOperationAsync(userID uint64, accessToken string, req dto.OperationCreateReq) (interface{}, error) {
	eventID := req.NamespaceID

	var Job JobRequest

	if req.Method == "apply" {
		Job = JobRequest{
			ID:          nil,
			Name:        "Create project",
			Description: "Create the project to digitalocean",
			StatusID:    1, // Assuming 1 is the status ID for pending
			EventID:     &eventID,
			Event:       "namespace",
			Action:      "create",
		}
	} else if req.Method == "destroy" {
		Job = JobRequest{
			ID:          nil,
			Name:        "Destroy project",
			Description: "Destroy the project from digitalocean",
			StatusID:    1, // Assuming 1 is the status ID for pending
			EventID:     &eventID,
			Event:       "namespace",
			Action:      "delete",
		}
	} else if req.Method == "plan" {
		Job = JobRequest{
			ID:          nil,
			Name:        "Plan project",
			Description: "Plan the project to digitalocean",
			StatusID:    1, // Assuming 1 is the status ID for pending
			EventID:     &eventID,
			Event:       "namespace",
			Action:      "plan",
		}
	}

	job, err := c.jobService.CreateJob(
		userID,
		Job.Name,
		Job.Description,
		Job.StatusID,
		Job.EventID,
		Job.Event,
		Job.Action,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to project job: %v", err)
	}
	Job.ID = &job.ID

	go func() {
		defer func() {
			if r := recover(); r != nil {
				fmt.Printf("Recovered from panic in CreateOperationAsync: %v\n", r)
			}
		}()
		result, err := c.executeCreateOperation(accessToken, req, Job, userID)
		if err != nil {
			fmt.Printf("Async project operation failed: %v\n", err)
		} else {
			fmt.Printf("Async project operation completed successfully: %v\n", result)
		}
	}()
	return "projects start create process.", nil
}

func (c *OperationService) executeCreateOperation(accessToken string, req dto.OperationCreateReq, Job JobRequest, userID uint64) (interface{}, error) {
	// Update job status to running (3)
	if err := c.updateJob(Job, userID, 3); err != nil {
		return nil, err
	}
	var statusID uint64
	if Job.Action == "create" {
		statusID = 2
	}
	if Job.Action == "plan" {
		statusID = 1
	}
	if Job.Action == "update" {
		statusID = 5
	}
	if Job.Action == "delete" {
		statusID = 7
	}
	err := c.UpdateClusterNamespaceStatuses(statusID, req.ClusterID, req.NamespaceID, userID)
	if err != nil {
		return nil, err
	}
	valueEndpoint := os.Getenv("TF_OPERATION_ENDPOINT")
	endpoint := fmt.Sprintf("%s/operations/%s?namespace_id=%s", valueEndpoint, strconv.Itoa(int(req.ClusterID)), strconv.Itoa(int(req.NamespaceID)))
	terraformDir := "./terraform/resource"
	env := os.Getenv("ENV")
	if env != "local" {
		terraformDir = "/app/terraform/resource"
	}
	workspaceName := fmt.Sprintf("namespace-%s", strconv.Itoa(int(req.NamespaceID)))

	// Get environment variables
	envDoToken := os.Getenv("DO_TOKEN")
	valueCloudflareApiToken := os.Getenv("CLOUDFLARE_API_TOKEN")
	valueCloudflareMasterZoneId := os.Getenv("CLOUDFLARE_MASTER_ZONE_ID")
	envDockerUsername := os.Getenv("DOCKERHUB_USERNAME")
	envDockerPassword := os.Getenv("DOCKERHUB_PASSWORD")
	envDockerEmail := os.Getenv("DOCKERHUB_EMAIL")
	envAwsAccessKeyID := os.Getenv("AWS_ACCESS_KEY_ID")
	envAwsSecretAccessKey := os.Getenv("AWS_SECRET_ACCESS_KEY")

	// Prepare variable flags for Terraform commands
	varFlags := []string{
		fmt.Sprintf("-var=do_token=%s", envDoToken),
		fmt.Sprintf("-var=operation_endpoint=%s", endpoint),
		fmt.Sprintf("-var=access_token=%s", accessToken),
		fmt.Sprintf("-var=cloudflare_api_token=%s", valueCloudflareApiToken),
		fmt.Sprintf("-var=cloudflare_zone_id=%s", valueCloudflareMasterZoneId),
		fmt.Sprintf("-var=dockerhub_username=%s", envDockerUsername),
		fmt.Sprintf("-var=dockerhub_password=%s", envDockerPassword),
		fmt.Sprintf("-var=dockerhub_email=%s", envDockerEmail),
		fmt.Sprintf("-var=aws_access_key_id=%s", envAwsAccessKeyID),
		fmt.Sprintf("-var=aws_secret_access_key=%s", envAwsSecretAccessKey),
	}

	valueDB := os.Getenv("TF_BACKEND_DB")
	valueSchema := os.Getenv("TF_BACKEND_SCHEMA_RESOURCE")
	// terraform init
	connStr := fmt.Sprintf("-backend-config=conn_str=%s", valueDB)
	schemaName := fmt.Sprintf("-backend-config=schema_name=%s", valueSchema)
	tfInit := exec.Command("terraform", "init",
		"-lock=false",
		connStr,
		schemaName,
	)
	tfInit.Dir = terraformDir
	tfInitOutput, tfInitErr := tfInit.CombinedOutput()
	if tfInitErr != nil {
		fmt.Printf("terraform init failed - output: %s\n", string(tfInitOutput))
		fmt.Printf("terraform init failed - error: %v\n", tfInitErr)

		// Update job status to failed (4) when terraform init fails
		if updateErr := c.updateJob(Job, userID, 4); updateErr != nil {
			fmt.Printf("failed to update job to failed status: %v\n", updateErr)
		}
		if err := c.updateClusterStatus(req.ClusterID, 8); err != nil {
			fmt.Printf("failed to update cluster status: %v\n", err)
		}

		return "terraform init failed", tfInitErr
	}
	fmt.Println("terraform init successful", string(tfInitOutput))
	// terraform select workspace or create workspace
	tfWorkspace := exec.Command("terraform", "workspace", "select", "-or-create", workspaceName)
	tfWorkspace.Dir = terraformDir
	tfWorkspaceOutput, tfWorkspaceErr := tfWorkspace.CombinedOutput()
	if tfWorkspaceErr != nil {
		fmt.Println("select or create workspace failed", string(tfWorkspaceOutput))
		return "select or create workspace failed", tfWorkspaceErr
	}
	fmt.Println("select or create workspace successful", string(tfWorkspaceOutput))
	// terraform plan
	if req.Method == "plan" {
		planArgs := append([]string{"plan"}, varFlags...)
		tfPlan := exec.Command("terraform", planArgs...)
		tfPlan.Dir = terraformDir
		tfPlanOutput, tfPlanErr := tfPlan.CombinedOutput()
		if tfPlanErr != nil {
			fmt.Println("terraform plan failed", string(tfPlanOutput))
			return "terraform plan failed", tfPlanErr
		}
		fmt.Println("terraform plan successful", string(tfPlanOutput))
	}
	//terraform apply
	if req.Method == "destroy" {
		// terraform destroy
		destroyArgs := append([]string{"destroy", "-auto-approve", "-lock=false"}, varFlags...)
		tfDestroy := exec.Command("terraform", destroyArgs...)
		tfDestroy.Dir = terraformDir
		tfDestroyOutput, tfDestroyErr := tfDestroy.CombinedOutput()
		if tfDestroyErr != nil {
			fmt.Println("terraform destroy failed", string(tfDestroyOutput))
			if updateErr := c.updateJob(Job, userID, 4); updateErr != nil {
				fmt.Printf("failed to update job to failed status: %v\n", updateErr)
			}
			return "terraform destroy failed", tfDestroyErr
		}
		fmt.Println("terraform destroy successful", string(tfDestroyOutput))
		// terraform delete workspace
		tfDefaultWorkspace := exec.Command("terraform", "workspace", "select", "-or-create", "default")
		tfDefaultWorkspace.Dir = terraformDir
		tfDefaultWorkspaceOutput, tfDefaultWorkspaceErr := tfDefaultWorkspace.CombinedOutput()
		if tfDefaultWorkspaceErr != nil {
			fmt.Println("select or create default workspace failed", string(tfDefaultWorkspaceOutput))
			if updateErr := c.updateJob(Job, userID, 4); updateErr != nil {
				fmt.Printf("failed to update job to failed status: %v\n", updateErr)
			}
			return "select or create default workspace failed", tfDefaultWorkspaceErr
		}
		fmt.Println("select or create default workspace successful", string(tfDefaultWorkspaceOutput))
		tfDeleteWorkspace := exec.Command("terraform", "workspace", "delete", workspaceName)
		tfDeleteWorkspace.Dir = terraformDir
		tfDeleteWorkspaceOutput, tfDeleteWorkspaceErr := tfDeleteWorkspace.CombinedOutput()
		if tfDeleteWorkspaceErr != nil {
			fmt.Println("delete workspace failed", string(tfDeleteWorkspaceOutput))
			if updateErr := c.updateJob(Job, userID, 4); updateErr != nil {
				fmt.Printf("failed to update job to failed status: %v\n", updateErr)
			}
			return "delete workspace failed", tfDeleteWorkspaceErr
		}
		fmt.Println("delete workspace successful", string(tfDeleteWorkspaceOutput))
		//setStatus, statusErr := c.GenerateRepository.UpdateShopStatus(shop.ShopId, false, false)
		//if statusErr != nil {
		//	fmt.Println("update shop status failed", setStatus)
		//	return "update shop status failed", statusErr
		//}
		//fmt.Println("update shop status successful", setStatus)
		if updateErr := c.updateJob(Job, userID, 5); updateErr != nil {
			fmt.Printf("failed to update job to failed status: %v\n", updateErr)
		}
		err := c.UpdateClusterNamespaceStatuses(9, req.ClusterID, req.NamespaceID, userID)
		if err != nil {
			return nil, err
		}

		_, err = c.DnsService.HandleDnsAsync(accessToken, dto.HandleDnsRequest{
			NamespaceID: req.NamespaceID,
			ZoneID:      "-",
			Method:      "destroy",
		})
		if err != nil {
			return nil, err
		}

		return "terraform destroy successful", tfDeleteWorkspaceErr
	}
	if req.Method == "apply" {
		applyArgs := append([]string{"apply", "-auto-approve"}, varFlags...)
		tfApply := exec.Command("terraform", applyArgs...)
		tfApply.Dir = terraformDir
		tfApplyOutput, tfApplyErr := tfApply.CombinedOutput()
		if tfApplyErr != nil {
			fmt.Println("terraform apply failed", string(tfApplyOutput))
			if updateErr := c.updateJob(Job, userID, 4); updateErr != nil {
				fmt.Printf("failed to update job to failed status: %v\n", updateErr)
			}
			return "terraform apply failed", tfApplyErr
		}
		fmt.Println("terraform apply successful", string(tfApplyOutput))

		if updateErr := c.updateJob(Job, userID, 5); updateErr != nil {
			fmt.Printf("failed to update job to failed status: %v\n", updateErr)
		}
		err := c.UpdateClusterNamespaceStatuses(3, req.ClusterID, req.NamespaceID, userID)
		if err != nil {
			return nil, err
		}

		return "terraform apply successful", tfApplyErr
	}
	return "method not found", nil
}

func (c *OperationService) CreateClusterAsync(userID uint64, accessToken string, req dto.OperationCreateReq) (interface{}, error) {
	eventID := req.ClusterID

	var Job JobRequest

	if req.Method == "apply" {
		Job = JobRequest{
			ID:          nil,
			Name:        "Create cluster",
			Description: "Create the cluster to digitalocean",
			StatusID:    1, // Assuming 1 is the status ID for pending
			EventID:     &eventID,
			Event:       "cluster",
			Action:      "create",
		}
	} else if req.Method == "destroy" {
		Job = JobRequest{
			ID:          nil,
			Name:        "Destroy cluster",
			Description: "Destroy the cluster from digitalocean",
			StatusID:    1, // Assuming 1 is the status ID for pending
			EventID:     &eventID,
			Event:       "cluster",
			Action:      "delete",
		}
	} else if req.Method == "plan" {
		Job = JobRequest{
			ID:          nil,
			Name:        "Plan cluster",
			Description: "Plan the cluster to digitalocean",
			StatusID:    1, // Assuming 1 is the status ID for pending
			EventID:     &eventID,
			Event:       "cluster",
			Action:      "plan",
		}
	}

	job, err := c.jobService.CreateJob(
		userID,
		Job.Name,
		Job.Description,
		Job.StatusID,
		Job.EventID,
		Job.Event,
		Job.Action,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create job: %v", err)
	}
	Job.ID = &job.ID

	// Return immediately with status message including job ID
	statusMsg := fmt.Sprintf("Cluster operation '%s' for cluster ID %d has been initiated and is running in background. Job ID: %d", req.Method, req.ClusterID, job.ID)

	// Execute the actual cluster operations in a goroutine
	go func() {
		defer func() {
			if r := recover(); r != nil {
				fmt.Printf("Recovered from panic in CreateClusterAsync: %v\n", r)
			}
		}()

		result, err := c.executeClusterOperation(accessToken, req, Job, userID)
		if err != nil {
			fmt.Printf("Async cluster operation failed: %v\n", err)
		} else {
			fmt.Printf("Async cluster operation completed successfully: %v\n", result)
		}
	}()

	return statusMsg, nil
}

func (c *OperationService) executeClusterOperation(accessToken string, req dto.OperationCreateReq, Job JobRequest, userID uint64) (interface{}, error) {
	// Update job status to running (3)
	if err := c.updateJob(Job, userID, 3); err != nil {
		return nil, err
	}
	var statusID uint64
	if Job.Action == "create" {
		statusID = 2
	}
	if Job.Action == "plan" {
		statusID = 1
	}
	if Job.Action == "update" {
		statusID = 5
	}
	if Job.Action == "delete" {
		statusID = 7
	}
	if err := c.updateClusterStatus(req.ClusterID, statusID); err != nil {
		return nil, fmt.Errorf("failed to update cluster status: %v", err)
	}

	valueEndpoint := os.Getenv("TF_OPERATION_ENDPOINT")
	endpoint := fmt.Sprintf("%s/operations/%s", valueEndpoint, strconv.Itoa(int(req.ClusterID)))
	terraformDir := "./terraform/cluster"
	env := os.Getenv("ENV")
	if env != "local" {
		terraformDir = "/app/terraform/cluster"
	}

	workspaceName := fmt.Sprintf("cluster-%s", strconv.Itoa(int(req.ClusterID)))

	envDoToken := os.Getenv("DO_TOKEN")
	// Prepare variable flags for Terraform commands
	varFlags := []string{
		fmt.Sprintf("-var=operation_endpoint=%s", endpoint),
		fmt.Sprintf("-var=access_token=%s", accessToken),
		fmt.Sprintf("-var=do_token=%s", envDoToken),
	}

	valueDB := os.Getenv("TF_BACKEND_DB")
	valueSchema := os.Getenv("TF_BACKEND_SCHEMA_CLUSTER")

	// Add Helm repositories before updating
	helmAddBitnami := exec.Command("helm", "repo", "add", "bitnami", "https://charts.bitnami.com/bitnami")
	helmAddBitnami.Dir = terraformDir
	helmAddBitnamiOutput, helmAddBitnamiErr := helmAddBitnami.CombinedOutput()
	if helmAddBitnamiErr != nil {
		fmt.Printf("helm repo add bitnami warning (might already exist): %s\n", string(helmAddBitnamiOutput))
	}

	// Add other common repositories if needed
	helmAddIngress := exec.Command("helm", "repo", "add", "ingress-nginx", "https://kubernetes.github.io/ingress-nginx")
	helmAddIngress.Dir = terraformDir
	helmAddIngressOutput, helmAddIngressErr := helmAddIngress.CombinedOutput()
	if helmAddIngressErr != nil {
		fmt.Printf("helm repo add ingress-nginx warning (might already exist): %s\n", string(helmAddIngressOutput))
	}

	// Update helm repositories (correct command)
	helmUpdate := exec.Command("helm", "repo", "update")
	helmUpdate.Dir = terraformDir
	helmUpdateOutput, helmUpdateErr := helmUpdate.CombinedOutput()
	if helmUpdateErr != nil {
		fmt.Println("helm repo update failed", string(helmUpdateOutput))
		return "helm repo update failed", helmUpdateErr
	}
	fmt.Println("helm repo update successful", string(helmUpdateOutput))

	// terraform init
	connStr := fmt.Sprintf("-backend-config=conn_str=%s", valueDB)
	schemaName := fmt.Sprintf("-backend-config=schema_name=%s", valueSchema)
	tfInit := exec.Command("terraform", "init", "-lock=false", connStr, schemaName)
	tfInit.Dir = terraformDir
	tfInitOutput, tfInitErr := tfInit.CombinedOutput()
	if tfInitErr != nil {
		fmt.Printf("terraform init failed - output: %s\n", string(tfInitOutput))
		fmt.Printf("terraform init failed - error: %v\n", tfInitErr)

		// Update job status to failed (4) when terraform init fails
		if updateErr := c.updateJob(Job, userID, 4); updateErr != nil {
			fmt.Printf("failed to update job to failed status: %v\n", updateErr)
		}
		return "terraform init failed", tfInitErr
	}
	fmt.Println("terraform init successful", string(tfInitOutput))

	// terraform select workspace or create workspace
	tfWorkspace := exec.Command("terraform", "workspace", "select", "-or-create", workspaceName)
	tfWorkspace.Dir = terraformDir
	tfWorkspaceOutput, tfWorkspaceErr := tfWorkspace.CombinedOutput()
	if tfWorkspaceErr != nil {
		fmt.Println("select or create workspace failed", string(tfWorkspaceOutput))
		return "select or create workspace failed", tfWorkspaceErr
	}
	fmt.Println("select or create workspace successful", string(tfWorkspaceOutput))

	// terraform plan
	if req.Method == "plan" {
		planArgs := append([]string{"plan"}, varFlags...)
		tfPlan := exec.Command("terraform", planArgs...)
		tfPlan.Dir = terraformDir
		tfPlanOutput, tfPlanErr := tfPlan.CombinedOutput()
		if tfPlanErr != nil {
			fmt.Println("terraform plan failed", string(tfPlanOutput))
			return "terraform plan failed", tfPlanErr
		}
		fmt.Println("terraform plan successful", string(tfPlanOutput))
		return "terraform plan successful", nil
	}

	// terraform destroy
	if req.Method == "destroy" {
		destroyArgs := append([]string{"destroy", "-auto-approve", "-lock=false"}, varFlags...)
		tfDestroyCmd := exec.Command("terraform", destroyArgs...)
		tfDestroyCmd.Dir = terraformDir
		tfDestroyOutput, tfDestroyErr := tfDestroyCmd.CombinedOutput()
		if tfDestroyErr != nil {
			fmt.Println("terraform destroy failed", string(tfDestroyOutput))

			// Update job status to failed (4) when terraform destroy fails
			if updateErr := c.updateJob(Job, userID, 4); updateErr != nil {
				fmt.Printf("failed to update job to failed status: %v\n", updateErr)
			}
			if err := c.updateClusterStatus(req.ClusterID, 8); err != nil {
				return nil, fmt.Errorf("failed to update cluster status: %v", err)
			}

			return "terraform destroy failed", tfDestroyErr
		}
		fmt.Println("terraform destroy successful", string(tfDestroyOutput))

		// terraform delete workspace
		tfDefaultWorkspace := exec.Command("terraform", "workspace", "select", "-or-create", "default")
		tfDefaultWorkspace.Dir = terraformDir
		tfDefaultWorkspaceOutput, tfDefaultWorkspaceErr := tfDefaultWorkspace.CombinedOutput()
		if tfDefaultWorkspaceErr != nil {
			fmt.Println("select or create default workspace failed", string(tfDefaultWorkspaceOutput))
			return "select or create default workspace failed", tfDefaultWorkspaceErr
		}
		fmt.Println("select or create default workspace successful", string(tfDefaultWorkspaceOutput))

		tfDeleteWorkspace := exec.Command("terraform", "workspace", "delete", workspaceName)
		tfDeleteWorkspace.Dir = terraformDir
		tfDeleteWorkspaceOutput, tfDeleteWorkspaceErr := tfDeleteWorkspace.CombinedOutput()
		if tfDeleteWorkspaceErr != nil {
			fmt.Println("delete workspace failed", string(tfDeleteWorkspaceOutput))
			return "delete workspace failed", tfDeleteWorkspaceErr
		}
		fmt.Println("delete workspace successful", string(tfDeleteWorkspaceOutput))

		// Update job status to completed (5) when terraform destroy is successful
		if err := c.updateJob(Job, userID, 5); err != nil {
			return nil, err
		}
		if err := c.updateClusterStatus(req.ClusterID, 9); err != nil {
			return nil, fmt.Errorf("failed to update cluster status: %v", err)
		}

		return "terraform destroy successful", nil
	}

	// terraform apply
	if req.Method == "apply" {
		applyArgs := append([]string{"apply", "-auto-approve"}, varFlags...)
		tfApplyCmd := exec.Command("terraform", applyArgs...)
		tfApplyCmd.Dir = terraformDir
		tfApplyOutput, tfApplyErr := tfApplyCmd.CombinedOutput()
		if tfApplyErr != nil {
			fmt.Println("terraform apply failed", string(tfApplyOutput))

			// Update job status to failed (4) when terraform apply fails
			if updateErr := c.updateJob(Job, userID, 4); updateErr != nil {
				fmt.Printf("failed to update job to failed status: %v\n", updateErr)
			}
			if err := c.updateClusterStatus(req.ClusterID, 8); err != nil {
				return nil, fmt.Errorf("failed to update cluster status: %v", err)
			}

			return "terraform apply failed", tfApplyErr
		}
		fmt.Println("terraform apply successful", string(tfApplyOutput))

		// Get Terraform outputs after successful apply
		outputs, outputErr := c.getTerraformOutputs(terraformDir)
		if outputErr != nil {
			fmt.Printf("Warning: Failed to get terraform outputs: %v\n", outputErr)
		} else {
			// You can access specific outputs like this:
			if loadBalancerIP, exists := outputs["load_balancer_ip"]; exists && !loadBalancerIP.Sensitive {
				fmt.Printf("Load Balancer IP: %v\n", loadBalancerIP.Value)

				// Convert the load balancer IP to string
				loadBalancerIPStr := fmt.Sprintf("%v", loadBalancerIP.Value)

				// Update the cluster with the load balancer IP
				updatedCluster, updateErr := c.clusterService.UpdateLoadBalancerIP(req.ClusterID, loadBalancerIPStr)
				if updateErr != nil {
					fmt.Printf("Warning: Failed to update cluster load balancer IP: %v\n", updateErr)
				} else {
					fmt.Printf("Successfully updated cluster %d with load balancer IP: %s\n", updatedCluster.ID, updatedCluster.LoadBalanceIP)
				}
			}
		}

		// Update job status to completed (5) when terraform apply is successful
		if err := c.updateJob(Job, userID, 5); err != nil {
			return nil, err
		}
		if err := c.updateClusterStatus(req.ClusterID, 3); err != nil {
			return nil, fmt.Errorf("failed to update cluster status: %v", err)
		}

		return "terraform apply successful", nil
	}

	return "method not found", nil
}

func (c *OperationService) updateJob(job JobRequest, userID uint64, statusID uint64) error {
	_, err := c.jobService.UpdateJob(
		*job.ID,
		userID,
		job.Name,
		job.Description,
		statusID,
		job.EventID,
		job.Event,
		job.Action,
	)
	if err != nil {
		return fmt.Errorf("failed to update job: %v", err)
	}
	return nil
}

func (s *OperationService) updateClusterStatus(clusterID uint64, statusID uint64) error {
	// Get the current cluster to preserve other fields
	cluster, err := s.clusterService.GetByID(clusterID)
	if err != nil {
		return fmt.Errorf("failed to get cluster: %v", err)
	}

	// Update the cluster with the new status while preserving other fields
	_, err = s.clusterService.Update(
		clusterID,
		cluster.Name,
		cluster.Region,
		cluster.PoolName,
		cluster.Size,
		cluster.NodeCount,
		cluster.WorkspaceID,
		statusID,
	)
	if err != nil {
		return fmt.Errorf("failed to update cluster status: %v", err)
	}

	return nil
}

func (s *OperationService) updateDeploymentStatus(deploymentID uint64, statusID uint64) error {
	// Get the current deployment to preserve other fields
	deployment, err := s.deploymentService.GetByID(deploymentID)
	if err != nil {
		return fmt.Errorf("failed to get deployment: %v", err)
	}

	// Update the deployment with the new status while preserving other fields
	_, err = s.deploymentService.Update(
		deploymentID,
		deployment.Name,
		deployment.Image,
		deployment.ContainerPort,
		deployment.Replicas,
		deployment.NamespaceID,
		statusID,
	)
	if err != nil {
		return fmt.Errorf("failed to update deployment status: %v", err)
	}

	return nil
}

func (s *OperationService) updateServiceStatus(serviceID uint64, statusID uint64) error {
	// Get the current service to preserve other fields
	service, err := s.serviceService.GetByID(serviceID)
	if err != nil {
		return fmt.Errorf("failed to get service: %v", err)
	}

	// Update the service with the new status while preserving other fields
	_, err = s.serviceService.Update(
		serviceID,
		service.Name,
		service.Port,
		service.TargetPort,
		service.Type,
		service.ClusterIP,
		service.ExternalIP,
		service.NamespaceID,
		service.DeploymentID,
		statusID,
	)
	if err != nil {
		return fmt.Errorf("failed to update service status: %v", err)
	}

	return nil
}

func (s *OperationService) updateIngressStatus(ingressID uint64, statusID uint64) error {
	// Get the current ingress to preserve other fields
	ingress, err := s.ingressService.GetByID(ingressID)
	if err != nil {
		return fmt.Errorf("failed to get ingress: %v", err)
	}

	// Update the ingress with the new status while preserving other fields
	_, err = s.ingressService.Update(
		ingressID,
		ingress.Name,
		ingress.Class,
		statusID,
	)
	if err != nil {
		return fmt.Errorf("failed to update ingress status: %v", err)
	}

	return nil
}

func (s *OperationService) UpdateClusterNamespaceStatuses(statusID uint64, clusterID uint64, namespaceID uint64, userID uint64) error {
	// Get cluster data with specific namespace
	cluster, err := s.clusterRepo.FindByID(clusterID, namespaceID)
	if err != nil {
		return fmt.Errorf("failed to get cluster: %v", err)
	}
	if cluster == nil {
		return errors.New("cluster not found")
	}

	// Find the specific namespace
	var targetNamespace *domain.Namespace
	for _, namespace := range cluster.Namespaces {
		if namespace.ID == namespaceID {
			targetNamespace = &namespace
			break
		}
	}

	if targetNamespace == nil {
		return errors.New("namespace not found in cluster")
	}

	var updateErrors []string
	var jobAction domain.JobAction
	if statusID == 2 {
		jobAction = "create"
	}
	if statusID == 1 {
		jobAction = "plan"
	}
	if statusID == 5 || statusID == 3 {
		jobAction = "update"
	}
	if statusID == 7 || statusID == 9 {
		jobAction = "delete"
	}

	// Update all deployments in the namespace
	for _, deployment := range targetNamespace.Deployments {
		var Job JobRequest
		Job = JobRequest{
			ID:          nil,
			Name:        fmt.Sprintf("%s deployment", jobAction),
			Description: fmt.Sprintf("%s the deployment to k8s", jobAction),
			StatusID:    1, // Creating
			EventID:     &deployment.ID,
			Event:       "deployment",
			Action:      jobAction,
		}
		if statusID != 9 && statusID != 3 {
			job, err := s.jobService.CreateJob(
				userID,
				Job.Name,
				Job.Description,
				Job.StatusID,
				Job.EventID,
				Job.Event,
				Job.Action,
			)
			if err != nil {
				return nil
			}
			Job.ID = &job.ID
		}
		if err := s.updateDeploymentStatus(deployment.ID, statusID); err != nil {
			if statusID != 9 && statusID != 3 {
				if err := s.updateJob(Job, userID, 4); err != nil {
					return nil
				}
			}
			updateErrors = append(updateErrors, fmt.Sprintf("deployment %d: %v", deployment.ID, err))
		}
		if statusID != 9 && statusID != 3 {
			if err := s.updateJob(Job, userID, 5); err != nil {
				return nil
			}
		}
	}

	// Update all services in the namespace
	for _, service := range targetNamespace.Services {
		var Job JobRequest
		Job = JobRequest{
			ID:          nil,
			Name:        fmt.Sprintf("%s service", jobAction),
			Description: fmt.Sprintf("%s the service to k8s", jobAction),
			StatusID:    1, // Creating
			EventID:     &service.ID,
			Event:       "service",
			Action:      jobAction,
		}
		if statusID != 9 && statusID != 3 {
			job, err := s.jobService.CreateJob(
				userID,
				Job.Name,
				Job.Description,
				Job.StatusID,
				Job.EventID,
				Job.Event,
				Job.Action,
			)
			if err != nil {
				return nil
			}
			Job.ID = &job.ID
		}
		if err := s.updateServiceStatus(service.ID, statusID); err != nil {
			if statusID != 9 && statusID != 3 {
				if err := s.updateJob(Job, userID, 4); err != nil {
					return nil
				}
			}
			updateErrors = append(updateErrors, fmt.Sprintf("service %d: %v", service.ID, err))
		}
		if statusID != 9 && statusID != 3 {
			if err := s.updateJob(Job, userID, 5); err != nil {
				return nil
			}
		}
	}

	// Update all ingresses in the namespace
	for _, ingress := range targetNamespace.Ingress {
		var Job JobRequest
		Job = JobRequest{
			ID:          nil,
			Name:        fmt.Sprintf("%s ingress", jobAction),
			Description: fmt.Sprintf("%s the ingress to k8s", jobAction),
			StatusID:    1, // Creating
			EventID:     &ingress.ID,
			Event:       "ingress",
			Action:      jobAction,
		}
		if statusID != 9 && statusID != 3 {
			job, err := s.jobService.CreateJob(
				userID,
				Job.Name,
				Job.Description,
				Job.StatusID,
				Job.EventID,
				Job.Event,
				Job.Action,
			)
			if err != nil {
				return nil
			}
			Job.ID = &job.ID
		}
		if err := s.updateIngressStatus(ingress.ID, statusID); err != nil {
			if statusID != 9 && statusID != 3 {
				if err := s.updateJob(Job, userID, 4); err != nil {
					return nil
				}
			}
			updateErrors = append(updateErrors, fmt.Sprintf("ingress %d: %v", ingress.ID, err))
		}
		if statusID != 9 && statusID != 3 {
			if err := s.updateJob(Job, userID, 5); err != nil {
				return nil
			}
		}
	}

	// Return combined errors if any occurred
	if len(updateErrors) > 0 {
		return fmt.Errorf("failed to update some statuses: %v", updateErrors)
	}

	// Update namespace type to published
	namespace, err := s.namespaceService.GetByID(namespaceID)
	if err != nil {
		return fmt.Errorf("failed to get namespace: %v", err)
	}

	_, err = s.namespaceService.Update(
		namespaceID,
		namespace.Name,
		namespace.Slug,
		namespace.IsActive,
		domain.NamespaceTypePublished,
		namespace.ClusterID,
	)
	if err != nil {
		return fmt.Errorf("failed to update namespace type to published: %v", err)
	}

	orderNamespaceFilter := ports.OrderNamespaceFilter{
		NamespaceID: &namespaceID,
	}
	orderNamespaces, err := s.orderNamespaceService.GetAll(&orderNamespaceFilter)
	if len(orderNamespaces) > 0 {
		for _, orderNamespace := range orderNamespaces {
			_, err := s.orderService.UpdateConfirmation(orderNamespace.OrderID, true)
			if err != nil {
				return fmt.Errorf("failed to update order confirmation: %v", err)
			}
		}
	}

	return nil
}

// getTerraformOutputs executes 'terraform output -json' and returns parsed outputs
func (c *OperationService) getTerraformOutputs(terraformDir string) (TerraformOutputs, error) {
	tfOutput := exec.Command("terraform", "output", "-json")
	tfOutput.Dir = terraformDir

	outputBytes, err := tfOutput.CombinedOutput()
	if err != nil {
		return nil, fmt.Errorf("terraform output command failed: %v, output: %s", err, string(outputBytes))
	}

	// Handle empty outputs
	outputStr := strings.TrimSpace(string(outputBytes))
	if outputStr == "" || outputStr == "{}" {
		return TerraformOutputs{}, nil
	}

	var outputs TerraformOutputs
	if err := json.Unmarshal(outputBytes, &outputs); err != nil {
		return nil, fmt.Errorf("failed to parse terraform outputs JSON: %v, raw output: %s", err, string(outputBytes))
	}

	return outputs, nil
}
